{"version": 3, "file": "ai.js", "sourceRoot": "", "sources": ["../../src/services/ai.ts"], "names": [], "mappings": "AAAA;;GAEG;AAGH,OAAO,EAAE,eAAe,EAAE,MAAM,uBAAuB,CAAC;AACxD,OAAO,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AAQ5C,MAAM,OAAO,SAAS;IACZ,aAAa,CAAgB;IAErC;QACE,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI,CACR,QAAuB,EACvB,UAAqC,EAAE;QAEvC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEzC,IAAI,CAAC;YACH,eAAe,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACvC,MAAM,MAAM,GAAG,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAEjD,MAAM,cAAc,GAAiC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACxE,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,OAAO,EAAE,GAAG,CAAC,OAAO;aACrB,CAAC,CAAC,CAAC;YAEJ,MAAM,gBAAgB,GAAQ;gBAC5B,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,QAAQ,EAAE,cAAc;aACzB,CAAC;YAEF,IAAI,MAAM,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;gBACrC,gBAAgB,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;YACpD,CAAC;YAED,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;gBACnC,gBAAgB,CAAC,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;YACjD,CAAC;YAED,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;YAE1E,MAAM,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;YACnE,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO;gBAC/B,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;oBACxB,YAAY,EAAE,UAAU,CAAC,KAAK,CAAC,aAAa;oBAC5C,gBAAgB,EAAE,UAAU,CAAC,KAAK,CAAC,iBAAiB;oBACpD,WAAW,EAAE,UAAU,CAAC,KAAK,CAAC,YAAY;iBAC3C,CAAC,CAAC,CAAC,SAAS;gBACb,KAAK,EAAE,UAAU,CAAC,KAAK;gBACvB,QAAQ,EAAE,MAAM,CAAC,QAAQ;aAC1B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACf,OAAe,EACf,YAAqB,EACrB,UAAqC,EAAE;QAEvC,MAAM,QAAQ,GAAkB,EAAE,CAAC;QAEnC,IAAI,YAAY,EAAE,CAAC;YACjB,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,YAAY;aACtB,CAAC,CAAC;QACL,CAAC;QAED,QAAQ,CAAC,IAAI,CAAC;YACZ,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,OAAO;SACjB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,OAAkC;QACpD,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;QACpD,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,YAAY,CAAC,eAAe,CAAC;QAClE,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,YAAY,CAAC,YAAY,CAAC;QAEzD,4CAA4C;QAC5C,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAEtE,cAAc;QACd,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM;YACd,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC;YACtC,cAAc,EAAE,MAAM,CAAC;QAEtC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CACb,iCAAiC,QAAQ,IAAI;gBAC7C,sDAAsD,CACvD,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAqB;YAC/B,QAAQ;YACR,KAAK;YACL,MAAM;YACN,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,cAAc,EAAE,WAAW,IAAI,YAAY,CAAC,WAAW;YAC3F,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,cAAc,EAAE,SAAS,IAAI,YAAY,CAAC,SAAS;SACpF,CAAC;QAEF,IAAI,OAAO,CAAC,OAAO,IAAI,cAAc,EAAE,OAAO,EAAE,CAAC;YAC/C,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,cAAc,EAAE,OAAO,CAAC;QAC9D,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,KAAc,EAAE,QAAgB;QAClD,MAAM,aAAa,GAAG,IAAI,KAAK,EAAmB,CAAC;QACnD,aAAa,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAElC,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YAC3B,aAAa,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;YACtC,aAAa,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;YAChC,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;gBAChB,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;YACpC,CAAC;YAED,gCAAgC;YAChC,IAAI,QAAQ,IAAI,KAAK,EAAE,CAAC;gBACtB,aAAa,CAAC,UAAU,GAAG,KAAK,CAAC,MAAgB,CAAC;YACpD,CAAC;YAED,IAAI,OAAO,IAAI,KAAK,EAAE,CAAC;gBACrB,aAAa,CAAC,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC;YACtC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,aAAa,CAAC,OAAO,GAAG,wCAAwC,QAAQ,EAAE,CAAC;YAC3E,aAAa,CAAC,OAAO,GAAG,KAAK,CAAC;QAChC,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;CACF"}