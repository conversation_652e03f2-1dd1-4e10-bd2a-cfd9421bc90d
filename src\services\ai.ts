/**
 * AI Service for handling chat completions across multiple providers
 */

import type { ChatCompletionMessageParam } from 'openai/resources/chat/completions';
import { ProviderFactory } from '../providers/index.js';
import { ConfigService } from './config.js';
import type { 
  AIProviderConfig, 
  ChatMessage, 
  AIResponse, 
  ProviderError 
} from '../types/index.js';

export class AIService {
  private configService: ConfigService;

  constructor() {
    this.configService = ConfigService.getInstance();
  }

  /**
   * Send a chat completion request
   */
  async chat(
    messages: ChatMessage[],
    options: Partial<AIProviderConfig> = {}
  ): Promise<AIResponse> {
    const config = this.buildConfig(options);
    
    try {
      ProviderFactory.validateConfig(config);
      const client = ProviderFactory.getClient(config);

      const openaiMessages: ChatCompletionMessageParam[] = messages.map(msg => ({
        role: msg.role,
        content: msg.content,
      }));

      const completionParams: any = {
        model: config.model,
        messages: openaiMessages,
      };

      if (config.temperature !== undefined) {
        completionParams.temperature = config.temperature;
      }

      if (config.maxTokens !== undefined) {
        completionParams.max_tokens = config.maxTokens;
      }

      const completion = await client.chat.completions.create(completionParams);

      const choice = completion.choices[0];
      if (!choice?.message?.content) {
        throw new Error('No response content received from AI provider');
      }

      return {
        content: choice.message.content,
        usage: completion.usage ? {
          promptTokens: completion.usage.prompt_tokens,
          completionTokens: completion.usage.completion_tokens,
          totalTokens: completion.usage.total_tokens,
        } : undefined,
        model: completion.model,
        provider: config.provider,
      };
    } catch (error) {
      throw this.handleError(error, config.provider);
    }
  }

  /**
   * Send a single message and get response
   */
  async sendMessage(
    message: string,
    systemPrompt?: string,
    options: Partial<AIProviderConfig> = {}
  ): Promise<AIResponse> {
    const messages: ChatMessage[] = [];
    
    if (systemPrompt) {
      messages.push({
        role: 'system',
        content: systemPrompt,
      });
    }

    messages.push({
      role: 'user',
      content: message,
    });

    return this.chat(messages, options);
  }

  /**
   * Build complete configuration from options and defaults
   */
  private buildConfig(options: Partial<AIProviderConfig>): AIProviderConfig {
    const globalConfig = this.configService.getConfig();
    const provider = options.provider || globalConfig.defaultProvider;
    const model = options.model || globalConfig.defaultModel;
    
    // Get provider-specific config if it exists
    const providerConfig = this.configService.getProviderConfig(provider);
    
    // Get API key
    const apiKey = options.apiKey || 
                   this.configService.getApiKey(provider) ||
                   providerConfig?.apiKey;

    if (!apiKey) {
      throw new Error(
        `No API key found for provider ${provider}. ` +
        `Please set the environment variable or configure it.`
      );
    }

    const config: AIProviderConfig = {
      provider,
      model,
      apiKey,
      temperature: options.temperature ?? providerConfig?.temperature ?? globalConfig.temperature,
      maxTokens: options.maxTokens ?? providerConfig?.maxTokens ?? globalConfig.maxTokens,
    };

    if (options.baseURL || providerConfig?.baseURL) {
      config.baseURL = options.baseURL || providerConfig?.baseURL;
    }

    return config;
  }

  /**
   * Handle and format errors from AI providers
   */
  private handleError(error: unknown, provider: string): ProviderError {
    const providerError = new Error() as ProviderError;
    providerError.provider = provider;

    if (error instanceof Error) {
      providerError.message = error.message;
      providerError.name = error.name;
      if (error.stack) {
        providerError.stack = error.stack;
      }
      
      // Handle OpenAI-specific errors
      if ('status' in error) {
        providerError.statusCode = error.status as number;
      }
      
      if ('error' in error) {
        providerError.details = error.error;
      }
    } else {
      providerError.message = `Unknown error occurred with provider ${provider}`;
      providerError.details = error;
    }

    return providerError;
  }
}
