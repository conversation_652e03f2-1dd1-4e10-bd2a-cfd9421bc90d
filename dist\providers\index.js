/**
 * AI Provider configurations and factory
 */
import OpenAI from 'openai';
import { AI_PROVIDERS } from '../types/index.js';
export class ProviderFactory {
    static instances = new Map();
    /**
     * Get or create an OpenAI client instance for the specified provider
     */
    static getClient(config) {
        const cacheKey = `${config.provider}-${config.baseURL || ''}`;
        if (this.instances.has(cacheKey)) {
            return this.instances.get(cacheKey);
        }
        const provider = AI_PROVIDERS[config.provider];
        if (!provider) {
            throw new Error(`Unknown provider: ${config.provider}`);
        }
        const client = new OpenAI({
            apiKey: config.apiKey,
            baseURL: config.baseURL || provider.baseURL,
        });
        this.instances.set(cacheKey, client);
        return client;
    }
    /**
     * Get available providers
     */
    static getAvailableProviders() {
        return Object.values(AI_PROVIDERS);
    }
    /**
     * Get provider by name
     */
    static getProvider(name) {
        return AI_PROVIDERS[name];
    }
    /**
     * Validate provider configuration
     */
    static validateConfig(config) {
        const provider = this.getProvider(config.provider);
        if (!provider) {
            throw new Error(`Unknown provider: ${config.provider}`);
        }
        if (!config.apiKey) {
            throw new Error(`API key is required for provider: ${config.provider}`);
        }
        if (!provider.models.includes(config.model)) {
            throw new Error(`Model ${config.model} is not supported by provider ${config.provider}. ` +
                `Supported models: ${provider.models.join(', ')}`);
        }
    }
    /**
     * Clear cached instances (useful for testing)
     */
    static clearCache() {
        this.instances.clear();
    }
}
/**
 * Default configurations for each provider
 */
export const DEFAULT_PROVIDER_CONFIGS = {
    openai: {
        provider: 'openai',
        model: 'gpt-4',
        temperature: 0.7,
        maxTokens: 2048,
    },
    deepseek: {
        provider: 'deepseek',
        model: 'deepseek-chat',
        temperature: 0.7,
        maxTokens: 2048,
    },
};
/**
 * Get default configuration for a provider
 */
export function getDefaultConfig(providerName) {
    return DEFAULT_PROVIDER_CONFIGS[providerName] || {};
}
//# sourceMappingURL=index.js.map