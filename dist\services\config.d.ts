/**
 * Configuration management service
 */
import type { C<PERSON><PERSON>onfig, AIProviderConfig, SavedChatSession } from '../types/index.js';
export declare class ConfigService {
    private static instance;
    private config;
    private configPath;
    private constructor();
    static getInstance(): ConfigService;
    /**
     * Get current configuration
     */
    getConfig(): CLIConfig;
    /**
     * Update configuration
     */
    updateConfig(updates: Partial<CLIConfig>): void;
    /**
     * Get provider configuration
     */
    getProviderConfig(providerName: string): AIProviderConfig | undefined;
    /**
     * Set provider configuration
     */
    setProviderConfig(providerName: string, config: AIProviderConfig): void;
    /**
     * Get API key for provider from environment or config
     */
    getApiKey(providerName: string): string | undefined;
    /**
     * Load configuration from file
     */
    private loadConfig;
    /**
     * Save configuration to file
     */
    private saveConfig;
    private saveConfigToFile;
    /**
     * Ensure config directory exists
     */
    private ensureConfigDir;
    /**
     * Reset configuration to defaults
     */
    resetConfig(): void;
    /**
     * Save a chat session configuration
     */
    saveChatSession(sessionConfig: {
        provider: string;
        model: string;
        temperature?: number | undefined;
        maxTokens?: number | undefined;
        systemPrompt?: string | undefined;
        name?: string;
    }): SavedChatSession;
    /**
     * Get saved chat sessions
     */
    getSavedSessions(): SavedChatSession[];
    /**
     * Get last used session
     */
    getLastUsedSession(): SavedChatSession | undefined;
    /**
     * Update last used session
     */
    updateLastUsedSession(sessionId: string): void;
    /**
     * Delete a saved session
     */
    deleteSavedSession(sessionId: string): void;
    /**
     * Check if there are any saved sessions with valid API keys
     */
    hasValidSavedSessions(): boolean;
}
//# sourceMappingURL=config.d.ts.map