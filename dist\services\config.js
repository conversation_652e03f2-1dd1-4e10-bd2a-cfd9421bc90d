/**
 * Configuration management service
 */
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { homedir } from 'os';
import { config as loadEnv } from 'dotenv';
import { AI_PROVIDERS } from '../types/index.js';
// Load environment variables
loadEnv();
export class ConfigService {
    static instance;
    config;
    configPath;
    constructor() {
        this.configPath = join(homedir(), '.arien-ai', 'config.json');
        this.config = this.loadConfig();
    }
    static getInstance() {
        if (!ConfigService.instance) {
            ConfigService.instance = new ConfigService();
        }
        return ConfigService.instance;
    }
    /**
     * Get current configuration
     */
    getConfig() {
        return { ...this.config };
    }
    /**
     * Update configuration
     */
    updateConfig(updates) {
        this.config = { ...this.config, ...updates };
        this.saveConfig();
    }
    /**
     * Get provider configuration
     */
    getProviderConfig(providerName) {
        return this.config.providers[providerName];
    }
    /**
     * Set provider configuration
     */
    setProviderConfig(providerName, config) {
        this.config.providers[providerName] = config;
        this.saveConfig();
    }
    /**
     * Get API key for provider from environment or config
     */
    getApiKey(providerName) {
        const provider = AI_PROVIDERS[providerName];
        if (!provider)
            return undefined;
        // First try environment variable
        const envKey = process.env[provider.apiKeyEnvVar];
        if (envKey)
            return envKey;
        // Then try config
        const providerConfig = this.getProviderConfig(providerName);
        return providerConfig?.apiKey;
    }
    /**
     * Load configuration from file
     */
    loadConfig() {
        const defaultConfig = {
            defaultProvider: 'openai',
            defaultModel: 'gpt-4',
            temperature: 0.7,
            maxTokens: 2048,
            providers: {},
            logLevel: 'info',
            savedSessions: {},
        };
        if (!existsSync(this.configPath)) {
            this.ensureConfigDir();
            this.saveConfigToFile(defaultConfig);
            return defaultConfig;
        }
        try {
            const configData = readFileSync(this.configPath, 'utf-8');
            const loadedConfig = JSON.parse(configData);
            // Merge with defaults to ensure all properties exist
            return { ...defaultConfig, ...loadedConfig };
        }
        catch (error) {
            console.warn(`Failed to load config from ${this.configPath}, using defaults:`, error);
            return defaultConfig;
        }
    }
    /**
     * Save configuration to file
     */
    saveConfig() {
        this.saveConfigToFile(this.config);
    }
    saveConfigToFile(config) {
        try {
            this.ensureConfigDir();
            writeFileSync(this.configPath, JSON.stringify(config, null, 2));
        }
        catch (error) {
            console.error(`Failed to save config to ${this.configPath}:`, error);
        }
    }
    /**
     * Ensure config directory exists
     */
    ensureConfigDir() {
        const configDir = dirname(this.configPath);
        if (!existsSync(configDir)) {
            mkdirSync(configDir, { recursive: true });
        }
    }
    /**
     * Reset configuration to defaults
     */
    resetConfig() {
        this.config = {
            defaultProvider: 'openai',
            defaultModel: 'gpt-4',
            temperature: 0.7,
            maxTokens: 2048,
            providers: {},
            logLevel: 'info',
            savedSessions: {},
        };
        this.saveConfig();
    }
    /**
     * Save a chat session configuration
     */
    saveChatSession(sessionConfig) {
        const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        const sessionName = sessionConfig.name || `${sessionConfig.provider}/${sessionConfig.model}`;
        const savedSession = {
            id: sessionId,
            name: sessionName,
            provider: sessionConfig.provider,
            model: sessionConfig.model,
            temperature: sessionConfig.temperature,
            maxTokens: sessionConfig.maxTokens,
            systemPrompt: sessionConfig.systemPrompt,
            lastUsed: new Date(),
            createdAt: new Date(),
        };
        this.config.savedSessions[sessionId] = savedSession;
        this.config.lastUsedSession = savedSession;
        this.saveConfig();
        return savedSession;
    }
    /**
     * Get saved chat sessions
     */
    getSavedSessions() {
        return Object.values(this.config.savedSessions).sort((a, b) => new Date(b.lastUsed).getTime() - new Date(a.lastUsed).getTime());
    }
    /**
     * Get last used session
     */
    getLastUsedSession() {
        return this.config.lastUsedSession;
    }
    /**
     * Update last used session
     */
    updateLastUsedSession(sessionId) {
        const session = this.config.savedSessions[sessionId];
        if (session) {
            session.lastUsed = new Date();
            this.config.lastUsedSession = session;
            this.saveConfig();
        }
    }
    /**
     * Delete a saved session
     */
    deleteSavedSession(sessionId) {
        delete this.config.savedSessions[sessionId];
        // If this was the last used session, clear it
        if (this.config.lastUsedSession?.id === sessionId) {
            this.config.lastUsedSession = undefined;
        }
        this.saveConfig();
    }
    /**
     * Check if there are any saved sessions with valid API keys
     */
    hasValidSavedSessions() {
        const sessions = this.getSavedSessions();
        return sessions.some(session => {
            const apiKey = this.getApiKey(session.provider);
            return apiKey && apiKey.length > 0;
        });
    }
}
//# sourceMappingURL=config.js.map