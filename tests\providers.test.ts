/**
 * Tests for provider functionality
 */

import { ProviderFactory } from '../src/providers/index.js';
import { AI_PROVIDERS } from '../src/types/index.js';

describe('ProviderFactory', () => {
  beforeEach(() => {
    ProviderFactory.clearCache();
  });

  describe('getAvailableProviders', () => {
    it('should return all available providers', () => {
      const providers = ProviderFactory.getAvailableProviders();
      expect(providers).toHaveLength(2);
      expect(providers.map(p => p.name)).toContain('OpenAI');
      expect(providers.map(p => p.name)).toContain('DeepSeek');
    });
  });

  describe('getProvider', () => {
    it('should return OpenAI provider', () => {
      const provider = ProviderFactory.getProvider('openai');
      expect(provider).toBeDefined();
      expect(provider?.name).toBe('OpenAI');
      expect(provider?.baseURL).toBe('https://api.openai.com/v1');
    });

    it('should return DeepSeek provider', () => {
      const provider = ProviderFactory.getProvider('deepseek');
      expect(provider).toBeDefined();
      expect(provider?.name).toBe('DeepSeek');
      expect(provider?.baseURL).toBe('https://api.deepseek.com/v1');
    });

    it('should return undefined for unknown provider', () => {
      const provider = ProviderFactory.getProvider('unknown');
      expect(provider).toBeUndefined();
    });
  });

  describe('validateConfig', () => {
    it('should validate valid OpenAI config', () => {
      const config = {
        provider: 'openai',
        model: 'gpt-4',
        apiKey: 'test-key',
      };

      expect(() => ProviderFactory.validateConfig(config)).not.toThrow();
    });

    it('should validate valid DeepSeek config', () => {
      const config = {
        provider: 'deepseek',
        model: 'deepseek-chat',
        apiKey: 'test-key',
      };

      expect(() => ProviderFactory.validateConfig(config)).not.toThrow();
    });

    it('should throw for unknown provider', () => {
      const config = {
        provider: 'unknown',
        model: 'some-model',
        apiKey: 'test-key',
      };

      expect(() => ProviderFactory.validateConfig(config)).toThrow('Unknown provider: unknown');
    });

    it('should throw for missing API key', () => {
      const config = {
        provider: 'openai',
        model: 'gpt-4',
        apiKey: '',
      };

      expect(() => ProviderFactory.validateConfig(config)).toThrow('API key is required');
    });

    it('should throw for unsupported model', () => {
      const config = {
        provider: 'openai',
        model: 'unsupported-model',
        apiKey: 'test-key',
      };

      expect(() => ProviderFactory.validateConfig(config)).toThrow('Model unsupported-model is not supported');
    });
  });
});

describe('AI_PROVIDERS', () => {
  it('should have correct OpenAI configuration', () => {
    const openai = AI_PROVIDERS.openai;
    expect(openai.name).toBe('OpenAI');
    expect(openai.baseURL).toBe('https://api.openai.com/v1');
    expect(openai.models).toContain('gpt-4');
    expect(openai.apiKeyEnvVar).toBe('OPENAI_API_KEY');
  });

  it('should have correct DeepSeek configuration', () => {
    const deepseek = AI_PROVIDERS.deepseek;
    expect(deepseek.name).toBe('DeepSeek');
    expect(deepseek.baseURL).toBe('https://api.deepseek.com/v1');
    expect(deepseek.models).toContain('deepseek-chat');
    expect(deepseek.models).toContain('deepseek-reasoner');
    expect(deepseek.apiKeyEnvVar).toBe('DEEPSEEK_API_KEY');
  });
});
