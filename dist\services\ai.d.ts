/**
 * AI Service for handling chat completions across multiple providers
 */
import type { AIProviderConfig, ChatMessage, AIResponse } from '../types/index.js';
export declare class AIService {
    private configService;
    constructor();
    /**
     * Send a chat completion request
     */
    chat(messages: ChatMessage[], options?: Partial<AIProviderConfig>): Promise<AIResponse>;
    /**
     * Send a single message and get response
     */
    sendMessage(message: string, systemPrompt?: string, options?: Partial<AIProviderConfig>): Promise<AIResponse>;
    /**
     * Build complete configuration from options and defaults
     */
    private buildConfig;
    /**
     * Handle and format errors from AI providers
     */
    private handleError;
}
//# sourceMappingURL=ai.d.ts.map