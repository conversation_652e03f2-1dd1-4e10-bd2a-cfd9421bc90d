/**
 * Logging utilities with colored output
 */
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';
export declare class Logger {
    private static instance;
    private configService;
    private constructor();
    static getInstance(): Logger;
    /**
     * Log debug message
     */
    debug(message: string, ...args: unknown[]): void;
    /**
     * Log info message
     */
    info(message: string, ...args: unknown[]): void;
    /**
     * Log warning message
     */
    warn(message: string, ...args: unknown[]): void;
    /**
     * Log error message
     */
    error(message: string, ...args: unknown[]): void;
    /**
     * Log success message
     */
    success(message: string, ...args: unknown[]): void;
    /**
     * Core logging method
     */
    private log;
    /**
     * Check if message should be logged based on log level
     */
    private shouldLog;
    /**
     * Get colored prefix for log level
     */
    private getPrefix;
    /**
     * Color message based on log level
     */
    private colorMessage;
}
export declare const logger: Logger;
//# sourceMappingURL=logger.d.ts.map