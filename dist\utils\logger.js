/**
 * Logging utilities with colored output
 */
import chalk from 'chalk';
import { ConfigService } from '../services/config.js';
export class Logger {
    static instance;
    configService;
    constructor() {
        this.configService = ConfigService.getInstance();
    }
    static getInstance() {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }
    /**
     * Log debug message
     */
    debug(message, ...args) {
        this.log('debug', message, ...args);
    }
    /**
     * Log info message
     */
    info(message, ...args) {
        this.log('info', message, ...args);
    }
    /**
     * Log warning message
     */
    warn(message, ...args) {
        this.log('warn', message, ...args);
    }
    /**
     * Log error message
     */
    error(message, ...args) {
        this.log('error', message, ...args);
    }
    /**
     * Log success message
     */
    success(message, ...args) {
        console.log(chalk.green('✓'), chalk.green(message), ...args);
    }
    /**
     * Core logging method
     */
    log(level, message, ...args) {
        const config = this.configService.getConfig();
        if (!this.shouldLog(level, config.logLevel)) {
            return;
        }
        const timestamp = new Date().toISOString();
        const prefix = this.getPrefix(level);
        const coloredMessage = this.colorMessage(level, message);
        console.log(`${chalk.gray(timestamp)} ${prefix} ${coloredMessage}`, ...args);
    }
    /**
     * Check if message should be logged based on log level
     */
    shouldLog(messageLevel, configLevel) {
        const levels = {
            debug: 0,
            info: 1,
            warn: 2,
            error: 3,
        };
        return levels[messageLevel] >= levels[configLevel];
    }
    /**
     * Get colored prefix for log level
     */
    getPrefix(level) {
        switch (level) {
            case 'debug':
                return chalk.blue('DEBUG');
            case 'info':
                return chalk.cyan('INFO ');
            case 'warn':
                return chalk.yellow('WARN ');
            case 'error':
                return chalk.red('ERROR');
            default:
                return chalk.white('LOG  ');
        }
    }
    /**
     * Color message based on log level
     */
    colorMessage(level, message) {
        switch (level) {
            case 'debug':
                return chalk.blue(message);
            case 'info':
                return chalk.white(message);
            case 'warn':
                return chalk.yellow(message);
            case 'error':
                return chalk.red(message);
            default:
                return message;
        }
    }
}
// Export singleton instance
export const logger = Logger.getInstance();
//# sourceMappingURL=logger.js.map